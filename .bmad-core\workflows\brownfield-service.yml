workflow:
  id: brownfield-service
  name: Brownfield Service/API Enhancement
  description: >-
    Agent workflow for enhancing existing backend services and APIs with new features,
    modernization, or performance improvements. Handles existing system analysis and safe integration.
  type: brownfield
  project_types:
    - service-modernization
    - api-enhancement
    - microservice-extraction
    - performance-optimization
    - integration-enhancement

  sequence:
    - step: service_analysis
      agent: architect
      action: analyze existing project and use task document-project
      creates: multiple documents per the document-project template
      notes: "Review existing service documentation, codebase, performance metrics, and identify integration dependencies."

    - agent: pm
      creates: prd.md
      uses: brownfield-prd-tmpl
      requires: existing_service_analysis
      notes: "Creates comprehensive PRD focused on service enhancement with existing system analysis. SAVE OUTPUT: Copy final prd.md to your project's docs/ folder."

    - agent: architect
      creates: architecture.md
      uses: brownfield-architecture-tmpl
      requires: prd.md
      notes: "Creates architecture with service integration strategy and API evolution planning. SAVE OUTPUT: Copy final architecture.md to your project's docs/ folder."

    - agent: po
      validates: all_artifacts
      uses: po-master-checklist
      notes: "Validates all documents for service integration safety and API compatibility. May require updates to any document."

    - agent: various
      updates: any_flagged_documents
      condition: po_checklist_issues
      notes: "If <PERSON><PERSON> finds issues, return to relevant agent to fix and re-export updated documents to docs/ folder."

    - workflow_end:
      action: move_to_ide
      notes: |
        Planning phase complete! Now transition to IDE Development:
        
        1. ENSURE DOCUMENTS ARE IN PROJECT:
           - Copy final prd.md to project's docs/prd.md
           - Copy final architecture.md to project's docs/architecture.md
           - All documents must be in the project before proceeding
        
        2. SHARD DOCUMENTS (in IDE):
           - Option A: Use PO agent to shard: @po then ask to shard docs/prd.md
           - Option B: Manual: Drag shard-doc task + docs/prd.md into chat
           - This creates docs/prd/ and docs/architecture/ folders with sharded content
        
        3. START DEVELOPMENT CYCLE:
           a. SM Agent (New Chat): @sm → *create
              - Creates next story from sharded docs
              - Review and approve story (Draft → Approved)
           
           b. Dev Agent (New Chat): @dev
              - Implements approved story
              - Updates File List with all changes
              - Marks story as "Review" when complete
           
           c. QA Agent (New Chat): @qa → review-story
              - Senior dev review with refactoring ability
              - Fixes small issues directly
              - Leaves checklist for remaining items
              - Updates story status (Review → Done or stays Review)
           
           d. If QA left unchecked items:
              - Dev Agent (New Chat): Address remaining items
              - Return to QA for final approval
        
        4. REPEAT: Continue cycle for all epic stories
        
        Reference: data#bmad-kb:IDE Development Workflow

  flow_diagram: |
    ```mermaid
    graph TD
        A[Start: Service Enhancement] --> B[analyst: analyze existing service]
        B --> C[pm: prd.md]
        C --> D[architect: architecture.md]
        D --> E[po: validate with po-master-checklist]
        E --> F{PO finds issues?}
        F -->|Yes| G[Return to relevant agent for fixes]
        F -->|No| H[Move to IDE Environment]
        G --> E

        style H fill:#90EE90
        style C fill:#FFE4B5
        style D fill:#FFE4B5
    ```

  decision_guidance:
    when_to_use:
      - Service enhancement requires coordinated stories
      - API versioning or breaking changes needed
      - Database schema changes required
      - Performance or scalability improvements needed
      - Multiple integration points affected

  handoff_prompts:
    analyst_to_pm: "Service analysis complete. Create comprehensive PRD with service integration strategy."
    pm_to_architect: "PRD ready. Save it as docs/prd.md, then create the service architecture."
    architect_to_po: "Architecture complete. Save it as docs/architecture.md. Please validate all artifacts for service integration safety."
    po_issues: "PO found issues with [document]. Please return to [agent] to fix and re-save the updated document."
    complete: "All planning artifacts validated and saved in docs/ folder. Move to IDE environment to begin development."
